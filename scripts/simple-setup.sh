#!/bin/bash

# Simplified Cinode Temporal Platform Setup Script
set -e

echo "🚀 Setting up Cinode Temporal Platform (Simplified)..."

# Check if required tools are installed
check_dependency() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 is not installed. Please install it first."
        exit 1
    fi
}

echo "📋 Checking dependencies..."
check_dependency "node"
check_dependency "npm"
check_dependency "docker"
check_dependency "docker-compose"

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ All dependencies are installed"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your actual configuration values"
else
    echo "✅ .env file already exists"
fi

# Install dependencies for each package individually
echo "📦 Installing dependencies..."

echo "  📦 Installing root dependencies..."
npm install

echo "  📦 Installing shared package dependencies..."
cd shared && npm install && cd ..

echo "  📦 Installing workflows dependencies..."
cd workflows && npm install && cd ..

echo "  📦 Installing api-gateway dependencies..."
cd api-gateway && npm install && cd ..

echo "  📦 Installing frontend dependencies..."
cd frontend && npm install && cd ..

echo "✅ All dependencies installed successfully"

# Build shared package first
echo "🔨 Building shared package..."
cd shared && npm run build && cd ..

# Start infrastructure services
echo "🐳 Starting infrastructure services..."
docker-compose up -d postgres redis minio mailhog

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check if services are running
echo "🔍 Checking service health..."
if docker-compose ps | grep -q "Up"; then
    echo "✅ Infrastructure services are running"
else
    echo "❌ Some services failed to start. Check docker-compose logs"
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📚 Next steps:"
echo "1. Update your .env file with actual API keys and configuration"
echo "2. Start Temporal server: npm run temporal:dev (in a new terminal)"
echo "3. Start services manually:"
echo "   - API Gateway: cd api-gateway && npm run dev"
echo "   - Workflows: cd workflows && npm run dev"
echo "   - Frontend: cd frontend && npm run dev"
echo ""
echo "🔧 Available services:"
echo "- Frontend: http://localhost:3000"
echo "- API Gateway: http://localhost:8000"
echo "- Temporal UI: http://localhost:8080"
echo "- Mailhog: http://localhost:8025"
echo "- MinIO: http://localhost:9001"
echo ""
echo "🔧 Manual start commands:"
echo "- npm run temporal:dev  # Start Temporal server"
echo "- docker-compose logs   # View service logs"
echo ""
