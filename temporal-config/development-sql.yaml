# Temporal Dynamic Configuration for Development with SQL
# This file contains dynamic configuration values for Temporal server

# Frontend service configuration
frontend.enableClientVersionCheck:
  - value: true
    constraints: {}

# History service configuration
history.maxAutoResetPoints:
  - value: 20
    constraints: {}

# Matching service configuration
matching.numTaskqueueWritePartitions:
  - value: 4
    constraints: {}

matching.numTaskqueueReadPartitions:
  - value: 4
    constraints: {}

# Worker service configuration
worker.executionScannerEnabled:
  - value: true
    constraints: {}

worker.executionScannerConcurrency:
  - value: 5
    constraints: {}

# System configuration
system.enableNamespaceNotActiveAutoForwarding:
  - value: true
    constraints: {}

# Archival configuration
archival.history.enabled:
  - value: false
    constraints: {}

archival.visibility.enabled:
  - value: false
    constraints: {}

# Visibility configuration
visibility.enableReadFromClosedExecutionV2:
  - value: true
    constraints: {}

# Persistence configuration
persistence.defaultStoreType:
  - value: "sql"
    constraints: {}

# Limits configuration
limit.maxIDLength:
  - value: 1000
    constraints: {}

limit.blobSizeLimitError:
  - value: 2097152
    constraints: {}

limit.blobSizeLimitWarn:
  - value: 262144
    constraints: {}

# Development specific settings
system.enableDebugMode:
  - value: true
    constraints: {}

# SQL specific configuration
sql.maxConns:
  - value: 20
    constraints: {}

sql.maxIdleConns:
  - value: 20
    constraints: {}

sql.maxConnLifetime:
  - value: "1h"
    constraints: {}
