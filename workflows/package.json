{"name": "@cinode-platform/workflows", "version": "1.0.0", "description": "Temporal workflows for Cinode Platform", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "concurrently \"tsc --watch\" \"nodemon dist/worker.js\"", "start": "node dist/worker.js", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest"}, "dependencies": {"@cinode-platform/shared": "file:../shared", "@temporalio/activity": "^1.8.0", "@temporalio/client": "^1.8.0", "@temporalio/worker": "^1.8.0", "@temporalio/workflow": "^1.8.0", "axios": "^1.6.0", "dotenv": "^16.3.0", "openai": "^4.20.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/uuid": "^9.0.0", "concurrently": "^8.2.2", "nodemon": "^3.0.0", "typescript": "^5.3.0"}}