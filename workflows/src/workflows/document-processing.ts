import { 
  proxyActivities, 
  defineSignal, 
  defineQuery,
  setHandler,
  condition,
  sleep
} from '@temporalio/workflow';
import type * as activities from '../activities/document-activities';
import { 
  DocumentProcessingWorkflowInput, 
  DocumentProcessingResult,
  ExtractedJobData 
} from '@cinode-platform/shared';

// Proxy activities with appropriate timeouts
const {
  fetchDocument,
  getDocumentContent,
  extractDataWithAI,
  extractDataWithRegex,
  saveExtractedData
} = proxyActivities<typeof activities>({
  startToCloseTimeout: '5 minutes',
  retry: {
    initialInterval: '1s',
    maximumInterval: '30s',
    maximumAttempts: 3,
  },
});

// Signals for external interaction
export const retryExtractionSignal = defineSignal<[string]>('retryExtraction');
export const cancelProcessingSignal = defineSignal('cancelProcessing');

// Queries for workflow state
export const getProcessingStatusQuery = defineQuery<string>('getProcessingStatus');
export const getExtractedDataQuery = defineQuery<ExtractedJobData | null>('getExtractedData');

export async function documentProcessingWorkflow(
  input: DocumentProcessingWorkflowInput
): Promise<DocumentProcessingResult> {
  let status = 'starting';
  let extractedData: ExtractedJobData | null = null;
  let cancelled = false;
  let retryMethod: string | null = null;

  // Set up signal and query handlers
  setHandler(retryExtractionSignal, (method: string) => {
    retryMethod = method;
  });

  setHandler(cancelProcessingSignal, () => {
    cancelled = true;
  });

  setHandler(getProcessingStatusQuery, () => status);
  setHandler(getExtractedDataQuery, () => extractedData);

  try {
    // Step 1: Fetch document metadata
    status = 'fetching_document';
    const document = await fetchDocument(input.documentId);
    
    if (cancelled) {
      status = 'cancelled';
      return { success: false, error: 'Processing cancelled by user' };
    }

    // Step 2: Get document content
    status = 'reading_content';
    const content = await getDocumentContent(input.documentId);
    
    if (!content || content.trim().length === 0) {
      status = 'failed';
      return { success: false, error: 'Document content is empty' };
    }

    // Step 3: Extract data based on method preference
    status = 'extracting_data';
    let result: DocumentProcessingResult;
    
    const extractionMethod = input.extractionMethod || 'auto';
    
    if (extractionMethod === 'ai' || extractionMethod === 'auto') {
      result = await extractDataWithAI(content);
      
      // If AI fails and method is auto, fallback to regex
      if (!result.success && extractionMethod === 'auto') {
        status = 'fallback_to_regex';
        await sleep('2s'); // Brief pause before fallback
        result = await extractDataWithRegex(content);
      }
    } else {
      result = await extractDataWithRegex(content);
    }

    // Handle retry signal during processing
    if (retryMethod) {
      status = `retrying_with_${retryMethod}`;
      if (retryMethod === 'ai') {
        result = await extractDataWithAI(content);
      } else if (retryMethod === 'regex') {
        result = await extractDataWithRegex(content);
      }
      retryMethod = null;
    }

    if (cancelled) {
      status = 'cancelled';
      return { success: false, error: 'Processing cancelled by user' };
    }

    if (!result.success) {
      status = 'extraction_failed';
      return result;
    }

    extractedData = result.data || null;

    // Step 4: Save extracted data
    if (extractedData) {
      status = 'saving_data';
      await saveExtractedData(input.documentId, extractedData);
    }

    status = 'completed';
    return {
      success: true,
      data: extractedData,
      confidence: result.confidence,
      extractionMethod: result.extractionMethod
    };

  } catch (error) {
    status = 'error';
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

// Helper workflow for batch document processing
export async function batchDocumentProcessingWorkflow(
  documentIds: string[],
  userId: string,
  extractionMethod?: 'ai' | 'regex' | 'auto'
): Promise<DocumentProcessingResult[]> {
  const results: DocumentProcessingResult[] = [];
  
  // Process documents in parallel with concurrency limit
  const concurrencyLimit = 3;
  const chunks: string[][] = [];
  
  for (let i = 0; i < documentIds.length; i += concurrencyLimit) {
    chunks.push(documentIds.slice(i, i + concurrencyLimit));
  }
  
  for (const chunk of chunks) {
    const chunkPromises = chunk.map(documentId =>
      documentProcessingWorkflow({
        documentId,
        userId,
        extractionMethod,
        correlationId: `batch-${Date.now()}`
      })
    );
    
    const chunkResults = await Promise.all(chunkPromises);
    results.push(...chunkResults);
  }
  
  return results;
}
