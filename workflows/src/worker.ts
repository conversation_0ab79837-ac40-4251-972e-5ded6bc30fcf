import { Worker } from '@temporalio/worker';
import { getEnvVar } from '@cinode-platform/shared';
import * as activities from './activities/document-activities';

async function run() {
  try {
    console.log('🔄 Starting Temporal Worker...');

    const worker = await Worker.create({
      workflowsPath: require.resolve('./workflows'),
      activities,
      taskQueue: getEnvVar('TEMPORAL_TASK_QUEUE', 'cinode-tasks'),
      namespace: getEnvVar('TEMPORAL_NAMESPACE', 'default'),
    });

    console.log('✅ Temporal Worker created successfully');
    console.log(`📋 Task Queue: ${getEnvVar('TEMPORAL_TASK_QUEUE', 'cinode-tasks')}`);
    console.log(`🏢 Namespace: ${getEnvVar('TEMPORAL_NAMESPACE', 'default')}`);

    await worker.run();
  } catch (error) {
    console.error('❌ Failed to start Temporal Worker:', error);
    process.exit(1);
  }
}

run().catch((err) => {
  console.error('❌ Worker crashed:', err);
  process.exit(1);
});
