import { Context } from '@temporalio/activity';
import axios from 'axios';
import { 
  Document, 
  ExtractedJobData, 
  DocumentProcessingResult,
  getEnvVar 
} from '@cinode-platform/shared';

// Activity to fetch document from document service
export async function fetchDocument(documentId: string): Promise<Document> {
  const documentServiceUrl = getEnvVar('DOCUMENT_SERVICE_URL', 'http://localhost:8002');
  
  try {
    const response = await axios.get(`${documentServiceUrl}/documents/${documentId}`);
    return response.data.data;
  } catch (error) {
    Context.current().log.error('Failed to fetch document', { documentId, error });
    throw new Error(`Failed to fetch document: ${error}`);
  }
}

// Activity to extract data using AI
export async function extractDataWithAI(content: string): Promise<DocumentProcessingResult> {
  const openaiApiKey = getEnvVar('OPENAI_API_KEY');
  
  if (!openaiApiKey) {
    return {
      success: false,
      error: 'OpenAI API key not configured'
    };
  }

  try {
    const response = await axios.post('https://api.openai.com/v1/chat/completions', {
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: 'You are an expert at extracting structured job information from documents. Always respond with valid JSON.'
        },
        {
          role: 'user',
          content: `Extract job information from this document and return as JSON with fields: title, description, startDate, endDate, location, company, rate, skills (array), requirements (array), benefits (array). Document content: ${content}`
        }
      ],
      temperature: 0.1,
      max_tokens: 1000,
      response_format: { type: 'json_object' }
    }, {
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json'
      }
    });

    const extractedData = JSON.parse(response.data.choices[0].message.content);
    
    return {
      success: true,
      data: extractedData,
      confidence: 0.8,
      extractionMethod: 'ai'
    };
  } catch (error) {
    Context.current().log.error('AI extraction failed', { error });
    return {
      success: false,
      error: `AI extraction failed: ${error}`,
      extractionMethod: 'ai'
    };
  }
}

// Activity to extract data using regex patterns
export async function extractDataWithRegex(content: string): Promise<DocumentProcessingResult> {
  try {
    const data: ExtractedJobData = {
      description: content.trim()
    };

    // Extract title
    const titlePatterns = [
      /titel[:\s]+([^\n\r]+)/i,
      /job title[:\s]+([^\n\r]+)/i,
      /position[:\s]+([^\n\r]+)/i,
      /^([^\n\r]{10,100})/m
    ];

    for (const pattern of titlePatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        data.title = match[1].trim();
        break;
      }
    }

    // Extract dates
    const startDatePatterns = [
      /start date[:\s]+([^\n]+)/i,
      /startdatum[:\s]+([^\n]+)/i,
      /från[:\s]+([^\n]+)/i
    ];

    for (const pattern of startDatePatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        data.startDate = match[1].trim();
        break;
      }
    }

    // Extract location
    const locationPatterns = [
      /location[:\s]+([^\n\r]+)/i,
      /plats[:\s]+([^\n\r]+)/i,
      /ort[:\s]+([^\n\r]+)/i
    ];

    for (const pattern of locationPatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        data.location = match[1].trim();
        break;
      }
    }

    // Extract rate
    const ratePatterns = [
      /rate[:\s]+([^\n\r]+)/i,
      /salary[:\s]+([^\n\r]+)/i,
      /lön[:\s]+([^\n\r]+)/i,
      /(\d+\s*(?:SEK|EUR|USD|kr)\s*\/?\s*(?:hour|day|month|år|timme|dag|månad))/i
    ];

    for (const pattern of ratePatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        data.rate = match[1].trim();
        break;
      }
    }

    return {
      success: true,
      data,
      confidence: 0.6,
      extractionMethod: 'regex'
    };
  } catch (error) {
    Context.current().log.error('Regex extraction failed', { error });
    return {
      success: false,
      error: `Regex extraction failed: ${error}`,
      extractionMethod: 'regex'
    };
  }
}

// Activity to save extracted data
export async function saveExtractedData(
  documentId: string, 
  extractedData: ExtractedJobData
): Promise<void> {
  const documentServiceUrl = getEnvVar('DOCUMENT_SERVICE_URL', 'http://localhost:8002');
  
  try {
    await axios.patch(`${documentServiceUrl}/documents/${documentId}/extracted-data`, {
      extractedData
    });
    
    Context.current().log.info('Extracted data saved successfully', { documentId });
  } catch (error) {
    Context.current().log.error('Failed to save extracted data', { documentId, error });
    throw new Error(`Failed to save extracted data: ${error}`);
  }
}

// Activity to get document content
export async function getDocumentContent(documentId: string): Promise<string> {
  const documentServiceUrl = getEnvVar('DOCUMENT_SERVICE_URL', 'http://localhost:8002');
  
  try {
    const response = await axios.get(`${documentServiceUrl}/documents/${documentId}/content`);
    return response.data.content;
  } catch (error) {
    Context.current().log.error('Failed to get document content', { documentId, error });
    throw new Error(`Failed to get document content: ${error}`);
  }
}
