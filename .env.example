# Database Configuration
DATABASE_URL=postgresql://cinode_user:cinode_password@localhost:5432/cinode_platform
REDIS_URL=redis://localhost:6379

# Temporal Configuration
TEMPORAL_ADDRESS=localhost:7233
TEMPORAL_NAMESPACE=default
TEMPORAL_TASK_QUEUE=cinode-tasks

# External API Configuration
CINODE_API_BASE=https://api.cinode.com
CINODE_APP_KEY=your_cinode_app_key_here
CINODE_COMPANY_ID=your_cinode_company_id_here
OPENAI_API_KEY=your_openai_api_key_here

# Authentication Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Email Configuration (Development - using Mailhog)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# Email Configuration (Production - example with Gmail)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password
# SMTP_FROM=<EMAIL>

# File Storage Configuration
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=cinode-documents
MINIO_USE_SSL=false

# Service Ports
API_GATEWAY_PORT=8000
AUTH_SERVICE_PORT=8001
DOCUMENT_SERVICE_PORT=8002
PROJECT_SERVICE_PORT=8003
TEMPLATE_SERVICE_PORT=8004
NOTIFICATION_SERVICE_PORT=8005
FRONTEND_PORT=3000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Development Configuration
NODE_ENV=development
DEBUG=cinode:*

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=.txt,.pdf,.doc,.docx,.rtf

# AI Configuration
AI_MODEL=gpt-4o-mini
AI_MAX_TOKENS=1000
AI_TEMPERATURE=0.1

# Workflow Configuration
WORKFLOW_TIMEOUT=300000
ACTIVITY_TIMEOUT=60000
RETRY_MAX_ATTEMPTS=3
