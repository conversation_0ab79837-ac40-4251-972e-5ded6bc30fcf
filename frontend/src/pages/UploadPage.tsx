import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { documentsApi } from '../services/api';
import { Document } from '@cinode-platform/shared';

export default function UploadPage() {
  const navigate = useNavigate();
  const [textContent, setTextContent] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadedDocument, setUploadedDocument] = useState<Document | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const response = await documentsApi.uploadDocument(file);
      
      if (response.success && response.data) {
        setUploadedFile(file);
        setUploadedDocument(response.data);
        
        // Read file content for text files
        if (file.type === 'text/plain') {
          const text = await file.text();
          setTextContent(text);
        } else {
          setTextContent(`File uploaded: ${file.name}\n\n[File content will be processed automatically]`);
        }
        
        toast.success('File uploaded successfully!');
      } else {
        throw new Error(response.error || 'Upload failed');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Upload failed';
      toast.error(message);
    } finally {
      setIsUploading(false);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleClearContent = () => {
    setTextContent('');
    setUploadedFile(null);
    setUploadedDocument(null);
  };

  const handleProceedToExtraction = () => {
    if (!textContent.trim() && !uploadedDocument) {
      toast.error('Please enter text or upload a file first');
      return;
    }

    // Store content and document info for the extraction page
    sessionStorage.setItem('jobContent', textContent);
    if (uploadedDocument) {
      sessionStorage.setItem('uploadedDocumentId', uploadedDocument.id);
      sessionStorage.setItem('uploadedFileName', uploadedDocument.originalName);
    }

    navigate('/extract');
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">Upload Job Assignment</h1>
          <p className="text-gray-600 mt-2">
            Paste job description text or upload a file to get started
          </p>
        </div>

        <div className="p-6 space-y-6">
          {/* Text Input Section */}
          <div>
            <label htmlFor="textContent" className="block text-sm font-medium text-gray-700 mb-2">
              Job Description Text
            </label>
            <textarea
              id="textContent"
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              placeholder="Paste your job description here..."
              className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 resize-none"
            />
          </div>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">or</span>
            </div>
          </div>

          {/* File Upload Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload File
            </label>
            <div
              {...getRootProps()}
              className={`
                border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                ${isDragActive 
                  ? 'border-blue-400 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
                }
                ${isUploading ? 'pointer-events-none opacity-50' : ''}
              `}
            >
              <input {...getInputProps()} />
              
              {isUploading ? (
                <div className="flex flex-col items-center">
                  <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-4" />
                  <p className="text-gray-600">Uploading file...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <Upload className="h-12 w-12 text-gray-400 mb-4" />
                  {isDragActive ? (
                    <p className="text-blue-600">Drop the file here...</p>
                  ) : (
                    <>
                      <p className="text-gray-600 mb-2">
                        Drag and drop a file here, or click to select
                      </p>
                      <p className="text-sm text-gray-500">
                        Supports: TXT, PDF, DOC, DOCX (max 10MB)
                      </p>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Uploaded File Info */}
            {uploadedFile && uploadedDocument && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 text-green-600 mr-2" />
                    <div>
                      <p className="text-sm font-medium text-green-800">
                        {uploadedFile.name}
                      </p>
                      <p className="text-xs text-green-600">
                        {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB • Uploaded successfully
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={handleClearContent}
                    className="text-green-600 hover:text-green-800"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6">
            <button
              onClick={handleClearContent}
              className="px-4 py-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Clear
            </button>
            
            <button
              onClick={handleProceedToExtraction}
              disabled={!textContent.trim() && !uploadedDocument}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              Extract Information
              <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
