import React, { useState, useCallback } from "react"
import { useNavigate } from "react-router-dom"
import { useDropzone } from "react-dropzone"
import { Loader2 } from "lucide-react"
import toast from "react-hot-toast"

interface Document {
  id: string
  originalName: string
  size: number
}

export default function UploadPage() {
  const navigate = useNavigate()
  const [textContent, setTextContent] = useState("")
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [uploadedDocument, setUploadedDocument] = useState<Document | null>(
    null
  )
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    setIsUploading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const mockDocument: Document = {
        id: `doc_${Date.now()}`,
        originalName: file.name,
        size: file.size,
      }

      const response = { success: true, data: mockDocument }

      if (response.success && response.data) {
        setUploadedFile(file)
        setUploadedDocument(response.data)

        // Read file content for text files
        if (file.type === "text/plain") {
          const text = await file.text()
          setTextContent(text)
        } else {
          setTextContent(
            `File uploaded: ${file.name}\n\n[File content will be processed automatically]`
          )
        }

        toast.success("File uploaded successfully!")
      } else {
        throw new Error(response.error || "Upload failed")
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Upload failed"
      toast.error(message)
    } finally {
      setIsUploading(false)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "text/plain": [".txt"],
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  const handleClearContent = () => {
    setTextContent("")
    setUploadedFile(null)
    setUploadedDocument(null)
  }

  const handleProceedToExtraction = () => {
    if (!textContent.trim() && !uploadedDocument) {
      toast.error("Please enter text or upload a file first")
      return
    }

    // Store content and document info for the extraction page
    sessionStorage.setItem("jobContent", textContent)
    if (uploadedDocument) {
      sessionStorage.setItem("uploadedDocumentId", uploadedDocument.id)
      sessionStorage.setItem("uploadedFileName", uploadedDocument.originalName)
    }

    navigate("/extract")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex justify-between items-center max-w-6xl mx-auto">
          <h1 className="text-xl font-medium text-blue-600">
            Cinode Job Uploader
          </h1>
          <span className="text-gray-400">H</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-5 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Upload Job Assignment
          </h1>
          <p className="text-gray-600">
            Paste job description text or upload a file to get started
          </p>
        </div>

        <div className="space-y-8">
          {/* Text Input Section */}
          <div>
            <label
              htmlFor="textContent"
              className="block font-semibold text-gray-900 mb-2"
            >
              Job Description Text:
            </label>
            <textarea
              id="textContent"
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              placeholder="Paste your job description here or upload a file below..."
              rows={10}
              className="w-full px-3 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-600 resize-vertical text-sm font-inherit min-h-[200px]"
            />
          </div>

          {/* File Upload Section */}
          <div>
            <div
              {...getRootProps()}
              className={`
                  border-2 border-dashed rounded-xl p-10 text-center cursor-pointer transition-all duration-200 bg-gray-50
                  ${
                    isDragActive
                      ? "border-blue-600 bg-blue-50"
                      : "border-gray-300 hover:border-blue-600 hover:bg-blue-50"
                  }
                  ${isUploading ? "pointer-events-none opacity-50" : ""}
                `}
            >
              <input {...getInputProps()} />

              {isUploading ? (
                <div className="flex flex-col items-center">
                  <Loader2 className="h-12 w-12 text-blue-500 animate-spin mb-4" />
                  <p className="text-gray-600">Uploading file...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  {/* Document Icon */}
                  <svg
                    width="48"
                    height="48"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    className="text-gray-400 mb-4"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10,9 9,9 8,9"></polyline>
                  </svg>

                  {isDragActive ? (
                    <p className="text-blue-600">Drop the file here...</p>
                  ) : (
                    <>
                      <p className="text-gray-600 mb-2">
                        Drag and drop files here
                      </p>
                      <p className="text-gray-400 text-sm mb-4">or</p>
                      <label className="inline-block px-5 py-2.5 bg-blue-600 text-white rounded-md cursor-pointer transition-colors hover:bg-blue-700 font-medium">
                        Choose File
                      </label>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* File Info Display */}
            {uploadedFile && uploadedDocument && (
              <div className="mt-4 p-3 bg-green-50 border-l-4 border-green-500 rounded-md">
                <p className="text-green-800 font-medium m-0">
                  📄 {uploadedFile.name}
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 justify-end mt-8">
            <button
              onClick={handleClearContent}
              className="px-6 py-3 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors font-medium"
            >
              Clear
            </button>

            <button
              onClick={handleProceedToExtraction}
              disabled={!textContent.trim() && !uploadedDocument}
              className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed font-medium transition-colors"
            >
              Extract Information →
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-gray-500">© 2025 Cinode Assignment Uploader</p>
        </div>
      </div>
    </div>
  )
}
