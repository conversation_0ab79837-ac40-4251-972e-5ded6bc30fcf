import React, { useState, useCallback } from "react"
import { useNavigate } from "react-router-dom"
import { useDropzone } from "react-dropzone"
import { Upload, FileText, X, Loader2 } from "lucide-react"
import toast from "react-hot-toast"

interface Document {
  id: string
  originalName: string
  size: number
}

export default function UploadPage() {
  const navigate = useNavigate()
  const [textContent, setTextContent] = useState("")
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [uploadedDocument, setUploadedDocument] = useState<Document | null>(
    null
  )
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (!file) return

    setIsUploading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const mockDocument: Document = {
        id: `doc_${Date.now()}`,
        originalName: file.name,
        size: file.size,
      }

      const response = { success: true, data: mockDocument }

      if (response.success && response.data) {
        setUploadedFile(file)
        setUploadedDocument(response.data)

        // Read file content for text files
        if (file.type === "text/plain") {
          const text = await file.text()
          setTextContent(text)
        } else {
          setTextContent(
            `File uploaded: ${file.name}\n\n[File content will be processed automatically]`
          )
        }

        toast.success("File uploaded successfully!")
      } else {
        throw new Error(response.error || "Upload failed")
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "Upload failed"
      toast.error(message)
    } finally {
      setIsUploading(false)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "text/plain": [".txt"],
      "application/pdf": [".pdf"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  const handleClearContent = () => {
    setTextContent("")
    setUploadedFile(null)
    setUploadedDocument(null)
  }

  const handleProceedToExtraction = () => {
    if (!textContent.trim() && !uploadedDocument) {
      toast.error("Please enter text or upload a file first")
      return
    }

    // Store content and document info for the extraction page
    sessionStorage.setItem("jobContent", textContent)
    if (uploadedDocument) {
      sessionStorage.setItem("uploadedDocumentId", uploadedDocument.id)
      sessionStorage.setItem("uploadedFileName", uploadedDocument.originalName)
    }

    navigate("/extract")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <h1 className="text-2xl font-semibold text-blue-600">
            Cinode Job Uploader
          </h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Upload Job Assignment
              </h2>
              <p className="text-gray-600">
                Paste job description text or upload a file to get started
              </p>
            </div>

            <div className="space-y-8">
              {/* Text Input Section */}
              <div>
                <label
                  htmlFor="textContent"
                  className="block text-lg font-medium text-gray-900 mb-3"
                >
                  Job Description Text:
                </label>
                <textarea
                  id="textContent"
                  value={textContent}
                  onChange={(e) => setTextContent(e.target.value)}
                  placeholder="Paste your job description here or upload a file below..."
                  className="w-full h-48 px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-base"
                />
              </div>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-base">
                  <span className="px-4 bg-white text-gray-500">or</span>
                </div>
              </div>

              {/* File Upload Section */}
              <div>
                <div
                  {...getRootProps()}
                  className={`
                    border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-all duration-200
                    ${
                      isDragActive
                        ? "border-blue-400 bg-blue-50"
                        : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
                    }
                    ${isUploading ? "pointer-events-none opacity-50" : ""}
                  `}
                >
                  <input {...getInputProps()} />

                  {isUploading ? (
                    <div className="flex flex-col items-center">
                      <Loader2 className="h-16 w-16 text-blue-500 animate-spin mb-4" />
                      <p className="text-gray-600 text-lg">Uploading file...</p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <Upload className="h-16 w-16 text-gray-400 mb-4" />
                      {isDragActive ? (
                        <p className="text-blue-600 text-lg">
                          Drop the file here...
                        </p>
                      ) : (
                        <>
                          <p className="text-gray-600 mb-2 text-lg">
                            Drag and drop files here
                          </p>
                          <p className="text-gray-500 mb-4">or</p>
                          <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                            Choose File
                          </button>
                        </>
                      )}
                    </div>
                  )}
                </div>

                {/* Uploaded File Info */}
                {uploadedFile && uploadedDocument && (
                  <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 text-green-600 mr-2" />
                        <div>
                          <p className="text-sm font-medium text-green-800">
                            {uploadedFile.name}
                          </p>
                          <p className="text-xs text-green-600">
                            {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB •
                            Uploaded successfully
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={handleClearContent}
                        className="text-green-600 hover:text-green-800"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between pt-8 border-t border-gray-200">
                <button
                  onClick={handleClearContent}
                  className="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Clear
                </button>

                <button
                  onClick={handleProceedToExtraction}
                  disabled={!textContent.trim() && !uploadedDocument}
                  className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed font-medium transition-colors flex items-center"
                >
                  Extract Information
                  <svg
                    className="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-gray-500">© 2025 Cinode Assignment Uploader</p>
        </div>
      </div>
    </div>
  )
}
