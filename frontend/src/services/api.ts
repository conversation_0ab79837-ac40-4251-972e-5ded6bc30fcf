import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  ApiResponse, 
  LoginRequest, 
  RegisterRequest, 
  User, 
  AuthTokens,
  Document,
  Project,
  Template,
  ExtractedJobData
} from '@cinode-platform/shared';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.clearAuthToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  setAuthToken(token: string) {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.client.defaults.headers.common['Authorization'];
  }

  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    url: string,
    data?: any,
    config?: any
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.client.request({
        method,
        url,
        data,
        ...config,
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        error: error.message || 'Network error',
        timestamp: new Date(),
      };
    }
  }

  // Auth endpoints
  async login(credentials: LoginRequest): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    return this.request('POST', '/api/auth/login', credentials);
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    return this.request('POST', '/api/auth/register', userData);
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<AuthTokens>> {
    return this.request('POST', '/api/auth/refresh', { refreshToken });
  }

  async logout(): Promise<ApiResponse<void>> {
    return this.request('POST', '/api/auth/logout');
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request('GET', '/api/auth/profile');
  }

  // Document endpoints
  async uploadDocument(file: File): Promise<ApiResponse<Document>> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.request('POST', '/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async getDocuments(params?: {
    page?: number;
    limit?: number;
    search?: string;
  }): Promise<ApiResponse<Document[]>> {
    return this.request('GET', '/api/documents', null, { params });
  }

  async getDocument(id: string): Promise<ApiResponse<Document>> {
    return this.request('GET', `/api/documents/${id}`);
  }

  async deleteDocument(id: string): Promise<ApiResponse<void>> {
    return this.request('DELETE', `/api/documents/${id}`);
  }

  // Project endpoints
  async getProjects(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ApiResponse<Project[]>> {
    return this.request('GET', '/api/projects', null, { params });
  }

  async getProject(id: string): Promise<ApiResponse<Project>> {
    return this.request('GET', `/api/projects/${id}`);
  }

  async createProject(projectData: Partial<Project>): Promise<ApiResponse<Project>> {
    return this.request('POST', '/api/projects', projectData);
  }

  async updateProject(id: string, projectData: Partial<Project>): Promise<ApiResponse<Project>> {
    return this.request('PATCH', `/api/projects/${id}`, projectData);
  }

  async deleteProject(id: string): Promise<ApiResponse<void>> {
    return this.request('DELETE', `/api/projects/${id}`);
  }

  // Template endpoints
  async getTemplates(params?: {
    page?: number;
    limit?: number;
    type?: string;
  }): Promise<ApiResponse<Template[]>> {
    return this.request('GET', '/api/templates', null, { params });
  }

  async getTemplate(id: string): Promise<ApiResponse<Template>> {
    return this.request('GET', `/api/templates/${id}`);
  }

  async createTemplate(templateData: Partial<Template>): Promise<ApiResponse<Template>> {
    return this.request('POST', '/api/templates', templateData);
  }

  async updateTemplate(id: string, templateData: Partial<Template>): Promise<ApiResponse<Template>> {
    return this.request('PATCH', `/api/templates/${id}`, templateData);
  }

  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    return this.request('DELETE', `/api/templates/${id}`);
  }

  // Workflow endpoints
  async startDocumentProcessing(documentId: string, extractionMethod?: string): Promise<ApiResponse<any>> {
    return this.request('POST', '/api/workflows/document-processing', {
      documentId,
      extractionMethod,
    });
  }

  async startProjectCreation(extractedData: ExtractedJobData, options?: {
    templateId?: string;
    autoCreateRoles?: boolean;
  }): Promise<ApiResponse<any>> {
    return this.request('POST', '/api/workflows/project-creation', {
      extractedData,
      ...options,
    });
  }

  async startTemplateEmail(templateId: string, variables: Record<string, any>, recipients: string[]): Promise<ApiResponse<any>> {
    return this.request('POST', '/api/workflows/template-email', {
      templateId,
      variables,
      recipients,
    });
  }

  async getWorkflowStatus(workflowId: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/api/workflows/${workflowId}/status`);
  }

  async getWorkflowResult(workflowId: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/api/workflows/${workflowId}/result`);
  }

  async cancelWorkflow(workflowId: string, reason?: string): Promise<ApiResponse<any>> {
    return this.request('POST', `/api/workflows/${workflowId}/cancel`, { reason });
  }

  async sendWorkflowSignal(workflowId: string, signalName: string, args?: any[]): Promise<ApiResponse<any>> {
    return this.request('POST', `/api/workflows/${workflowId}/signal`, {
      signalName,
      args,
    });
  }

  async queryWorkflow(workflowId: string, queryName: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/api/workflows/${workflowId}/query/${queryName}`);
  }

  async getWorkflows(params?: {
    page?: number;
    limit?: number;
    status?: string;
    workflowType?: string;
  }): Promise<ApiResponse<any[]>> {
    return this.request('GET', '/api/workflows', null, { params });
  }

  // Health check
  async getHealth(): Promise<ApiResponse<any>> {
    return this.request('GET', '/health');
  }
}

// Create and export API instances
export const authApi = new ApiClient();
export const documentsApi = new ApiClient();
export const projectsApi = new ApiClient();
export const templatesApi = new ApiClient();
export const workflowsApi = new ApiClient();

// Export default instance
export default new ApiClient();
