import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, AuthTokens, LoginRequest } from '@cinode-platform/shared';
import { authApi } from '../services/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [tokens, setTokens] = useState<AuthTokens | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!tokens;

  // Load stored auth data on mount
  useEffect(() => {
    const storedTokens = localStorage.getItem('auth_tokens');
    const storedUser = localStorage.getItem('auth_user');

    if (storedTokens && storedUser) {
      try {
        const parsedTokens = JSON.parse(storedTokens);
        const parsedUser = JSON.parse(storedUser);
        
        // Check if token is expired
        const tokenExpiry = parsedTokens.expiresIn * 1000; // Convert to milliseconds
        const now = Date.now();
        
        if (now < tokenExpiry) {
          setTokens(parsedTokens);
          setUser(parsedUser);
        } else {
          // Token expired, clear storage
          localStorage.removeItem('auth_tokens');
          localStorage.removeItem('auth_user');
        }
      } catch (error) {
        console.error('Error parsing stored auth data:', error);
        localStorage.removeItem('auth_tokens');
        localStorage.removeItem('auth_user');
      }
    }
    
    setIsLoading(false);
  }, []);

  // Set up axios interceptor for token refresh
  useEffect(() => {
    if (tokens) {
      authApi.setAuthToken(tokens.accessToken);
    }
  }, [tokens]);

  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true);
      const response = await authApi.login(credentials);
      
      if (response.success && response.data) {
        const { user: userData, tokens: tokenData } = response.data;
        
        setUser(userData);
        setTokens(tokenData);
        
        // Store in localStorage
        localStorage.setItem('auth_tokens', JSON.stringify(tokenData));
        localStorage.setItem('auth_user', JSON.stringify(userData));
        
        // Set auth token for future requests
        authApi.setAuthToken(tokenData.accessToken);
        
        toast.success('Login successful!');
      } else {
        throw new Error(response.error || 'Login failed');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setTokens(null);
    
    // Clear localStorage
    localStorage.removeItem('auth_tokens');
    localStorage.removeItem('auth_user');
    
    // Clear auth token
    authApi.clearAuthToken();
    
    toast.success('Logged out successfully');
  };

  const refreshToken = async () => {
    if (!tokens?.refreshToken) {
      logout();
      return;
    }

    try {
      const response = await authApi.refreshToken(tokens.refreshToken);
      
      if (response.success && response.data) {
        const newTokens = response.data;
        
        setTokens(newTokens);
        
        // Update localStorage
        localStorage.setItem('auth_tokens', JSON.stringify(newTokens));
        
        // Set new auth token
        authApi.setAuthToken(newTokens.accessToken);
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    tokens,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
