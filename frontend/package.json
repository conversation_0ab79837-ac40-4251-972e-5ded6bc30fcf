{"name": "@cinode-platform/frontend", "version": "1.0.0", "description": "React frontend for Cinode Platform", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "test": "vitest", "test:ui": "vitest --ui", "clean": "rm -rf dist"}, "dependencies": {"@cinode-platform/shared": "file:../shared", "@heroicons/react": "^2.2.0", "axios": "^1.6.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.0", "react-query": "^3.39.0", "react-router-dom": "^6.20.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vitest": "^1.0.0"}}