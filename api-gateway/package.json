{"name": "@cinode-platform/api-gateway", "version": "1.0.0", "description": "API Gateway for Cinode Platform", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "concurrently \"tsc --watch\" \"nodemon dist/index.js\"", "start": "node dist/index.js", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest"}, "dependencies": {"@cinode-platform/shared": "file:../shared", "@temporalio/client": "^1.8.0", "express": "^4.18.0", "express-rate-limit": "^7.1.0", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "compression": "^1.7.4", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "multer": "^1.4.4", "axios": "^1.6.0", "dotenv": "^16.3.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/morgan": "^1.9.0", "@types/compression": "^1.7.0", "@types/jsonwebtoken": "^9.0.0", "@types/bcryptjs": "^2.4.0", "@types/multer": "^1.4.0", "@types/node": "^20.10.0", "@types/uuid": "^9.0.0", "concurrently": "^8.2.2", "nodemon": "^3.0.0", "typescript": "^5.3.0"}}