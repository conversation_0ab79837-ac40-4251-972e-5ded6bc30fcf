import { Request, Response, NextFunction } from "express"

type AsyncFunction = (
  req: Request,
  res: Response,
  next: NextFunction
) => Promise<any>

export const asyncHandler = (fn: AsyncFunction) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

export class AppError extends Error {
  public statusCode: number
  public code: string

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = "INTERNAL_ERROR"
  ) {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.name = "AppError"
  }
}

export const createAppError = (
  message: string,
  statusCode: number = 500,
  code: string = "INTERNAL_ERROR"
) => {
  return new AppError(message, statusCode, code)
}
