import { Request, Response, NextFunction } from "express"
import jwt from "jsonwebtoken"
import {
  getEnvVar,
  createErrorResponse,
  ErrorCode,
} from "@cinode-platform/shared"

interface JwtPayload {
  id: string
  email: string
  role: string
  iat: number
  exp: number
}

export function authMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  try {
    const authHeader = req.headers.authorization

    if (!authHeader) {
      res
        .status(401)
        .json(
          createErrorResponse(
            "Authorization header is required",
            ErrorCode.AUTHENTICATION_ERROR
          )
        )
      return
    }

    const token = authHeader.split(" ")[1] // Bearer <token>

    if (!token) {
      res
        .status(401)
        .json(
          createErrorResponse(
            "Token is required",
            ErrorCode.AUTHENTICATION_ERROR
          )
        )
      return
    }

    const jwtSecret = getEnvVar("JWT_SECRET")
    const decoded = jwt.verify(token, jwtSecret) as JwtPayload

    // Add user info to request
    req.user = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
    }

    next()
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      res
        .status(401)
        .json(
          createErrorResponse("Invalid token", ErrorCode.AUTHENTICATION_ERROR)
        )
      return
    }

    if (error instanceof jwt.TokenExpiredError) {
      res
        .status(401)
        .json(
          createErrorResponse("Token expired", ErrorCode.AUTHENTICATION_ERROR)
        )
      return
    }

    res
      .status(500)
      .json(
        createErrorResponse("Authentication error", ErrorCode.INTERNAL_ERROR)
      )
  }
}

export function requireRole(roles: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res
        .status(401)
        .json(
          createErrorResponse(
            "Authentication required",
            ErrorCode.AUTHENTICATION_ERROR
          )
        )
    }

    if (!roles.includes(req.user.role)) {
      return res
        .status(403)
        .json(
          createErrorResponse(
            "Insufficient permissions",
            ErrorCode.AUTHORIZATION_ERROR
          )
        )
    }

    next()
  }
}

export function requireAdmin(req: Request, res: Response, next: NextFunction) {
  return requireRole(["admin"])(req, res, next)
}

export function requireManagerOrAdmin(
  req: Request,
  res: Response,
  next: NextFunction
) {
  return requireRole(["admin", "manager"])(req, res, next)
}
