// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  USER = 'user'
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

// Document Types
export interface Document {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  uploadedBy: string;
  uploadedAt: Date;
  extractedData?: ExtractedJobData;
}

export interface ExtractedJobData {
  title?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  location?: string;
  company?: string;
  rate?: string;
  skills?: string[];
  requirements?: string[];
  benefits?: string[];
}

export interface DocumentProcessingResult {
  success: boolean;
  data?: ExtractedJobData;
  confidence?: number;
  extractionMethod?: 'ai' | 'regex';
  error?: string;
}

// Project Types
export interface Project {
  id: string;
  cinodeProjectId?: string;
  title: string;
  description: string;
  startDate?: Date;
  endDate?: Date;
  location?: string;
  company?: string;
  rate?: string;
  status: ProjectStatus;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  roles: ProjectRole[];
}

export enum ProjectStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface ProjectRole {
  id: string;
  projectId: string;
  cinodeRoleId?: string;
  title: string;
  description: string;
  skills: string[];
  requirements: string[];
  rate?: string;
  startDate?: Date;
  endDate?: Date;
  status: RoleStatus;
}

export enum RoleStatus {
  OPEN = 'open',
  FILLED = 'filled',
  CLOSED = 'closed'
}

// Template Types
export interface Template {
  id: string;
  name: string;
  description: string;
  type: TemplateType;
  subject: string;
  content: string;
  variables: TemplateVariable[];
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum TemplateType {
  EMAIL = 'email',
  SMS = 'sms',
  DOCUMENT = 'document'
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  required: boolean;
  defaultValue?: string;
  description?: string;
}

export interface TemplateRenderRequest {
  templateId: string;
  variables: Record<string, any>;
}

export interface TemplateRenderResult {
  subject: string;
  content: string;
  renderedAt: Date;
}

// Notification Types
export interface EmailNotification {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  content: string;
  isHtml?: boolean;
  attachments?: EmailAttachment[];
}

export interface EmailAttachment {
  filename: string;
  content: Buffer;
  contentType: string;
}

export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  sentAt: Date;
}

// Workflow Types
export interface WorkflowInput {
  userId: string;
  correlationId?: string;
  metadata?: Record<string, any>;
}

export interface DocumentProcessingWorkflowInput extends WorkflowInput {
  documentId: string;
  extractionMethod?: 'ai' | 'regex' | 'auto';
}

export interface ProjectCreationWorkflowInput extends WorkflowInput {
  extractedData: ExtractedJobData;
  templateId?: string;
  autoCreateRoles?: boolean;
}

export interface TemplateEmailWorkflowInput extends WorkflowInput {
  templateId: string;
  variables: Record<string, any>;
  recipients: string[];
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
}

export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  WORKFLOW_ERROR = 'WORKFLOW_ERROR'
}

// Health Check Types
export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Date;
  uptime: number;
  version: string;
  dependencies: HealthCheckDependency[];
}

export interface HealthCheckDependency {
  name: string;
  status: 'healthy' | 'unhealthy';
  responseTime?: number;
  error?: string;
}
